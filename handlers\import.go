package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"save-it/middleware"
	"save-it/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// ImportHandler handles data import functionality
type ImportHandler struct {
	db                *gorm.DB
	metadataExtractor *MetadataExtractor
}

// NewImportHandler creates a new import handler
func NewImportHandler(db *gorm.DB) *ImportHandler {
	return &ImportHandler{
		db:                db,
		metadataExtractor: NewMetadataExtractor(db),
	}
}

// PocketItem represents a single item from Pocket export
type PocketItem struct {
	ItemID        string            `json:"item_id"`
	ResolvedID    string            `json:"resolved_id"`
	GivenURL      string            `json:"given_url"`
	ResolvedURL   string            `json:"resolved_url"`
	GivenTitle    string            `json:"given_title"`
	ResolvedTitle string            `json:"resolved_title"`
	Favorite      string            `json:"favorite"`
	Status        string            `json:"status"`
	TimeAdded     string            `json:"time_added"`
	TimeUpdated   string            `json:"time_updated"`
	TimeRead      string            `json:"time_read"`
	TimeFavorited string            `json:"time_favorited"`
	SortID        int               `json:"sort_id"`
	Excerpt       string            `json:"excerpt"`
	IsArticle     string            `json:"is_article"`
	HasImage      string            `json:"has_image"`
	HasVideo      string            `json:"has_video"`
	WordCount     string            `json:"word_count"`
	Tags          map[string]string `json:"tags,omitempty"`
}

// PocketExport represents the structure of a Pocket export file
type PocketExport struct {
	Status   int                   `json:"status"`
	Complete int                   `json:"complete"`
	List     map[string]PocketItem `json:"list"`
	Error    interface{}           `json:"error"`
	Search   interface{}           `json:"search"`
	Since    int64                 `json:"since"`
}

// ImportResponse represents the response after importing data
type ImportResponse struct {
	Message       string   `json:"message"`
	TotalItems    int      `json:"total_items"`
	ImportedItems int      `json:"imported_items"`
	SkippedItems  int      `json:"skipped_items"`
	Errors        []string `json:"errors,omitempty"`
}

// ImportPocketData imports data from a Pocket JSON export
func (h *ImportHandler) ImportPocketData(c *gin.Context) {
	// Get user from context
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Authentication required",
		})
		return
	}

	// Get the uploaded file
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "No file uploaded or invalid file",
		})
		return
	}
	defer file.Close()

	// Check file size (limit to 50MB)
	if header.Size > 50*1024*1024 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "File too large (max 50MB)",
		})
		return
	}

	// First, read the beginning of the file to check for issues
	file.Seek(0, 0) // Reset file pointer
	firstBytes := make([]byte, 100)
	n, _ := file.Read(firstBytes)
	firstBytes = firstBytes[:n]

	// Check for BOM or other encoding issues
	if len(firstBytes) >= 3 && firstBytes[0] == 0xEF && firstBytes[1] == 0xBB && firstBytes[2] == 0xBF {
		// UTF-8 BOM detected, skip it
		file.Seek(3, 0)
	} else {
		file.Seek(0, 0)
	}

	// Try to parse as raw JSON to check structure
	var rawData map[string]interface{}
	decoder := json.NewDecoder(file)
	if err := decoder.Decode(&rawData); err != nil {
		// Try to provide more helpful error information
		errorMsg := "Invalid JSON format"
		maxLen := 50
		if len(firstBytes) < maxLen {
			maxLen = len(firstBytes)
		}
		debugInfo := fmt.Sprintf("First %d bytes: %q", maxLen, string(firstBytes[:maxLen]))

		if jsonErr, ok := err.(*json.SyntaxError); ok {
			errorMsg = fmt.Sprintf("JSON syntax error at byte offset %d", jsonErr.Offset)
		} else if jsonErr, ok := err.(*json.UnmarshalTypeError); ok {
			errorMsg = fmt.Sprintf("JSON type error: cannot unmarshal %s into %s", jsonErr.Value, jsonErr.Type)
		}

		c.JSON(http.StatusBadRequest, gin.H{
			"error":   errorMsg,
			"details": err.Error(),
			"debug":   debugInfo,
		})
		return
	}

	// Check if this looks like a Pocket export
	if _, hasStatus := rawData["status"]; !hasStatus {
		if _, hasList := rawData["list"]; !hasList {
			// Maybe it's a direct list of items
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "Unrecognized file format",
				"details": "This doesn't appear to be a Pocket export file. Expected fields 'status' and 'list' not found.",
			})
			return
		}
	}

	// Now parse as PocketExport
	file.Seek(0, 0) // Reset file pointer again
	var pocketData PocketExport
	decoder = json.NewDecoder(file)
	if err := decoder.Decode(&pocketData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Failed to parse Pocket export format",
			"details": err.Error(),
		})
		return
	}

	// Validate that we have the expected structure
	if pocketData.List == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid Pocket export format: missing 'list' field",
			"details": "The file doesn't appear to be a valid Pocket export",
		})
		return
	}

	// Process the import
	response := h.processPocketImport(userID, &pocketData)

	c.JSON(http.StatusOK, response)
}

// processPocketImport processes the Pocket data and imports items
func (h *ImportHandler) processPocketImport(userID uint, pocketData *PocketExport) ImportResponse {
	response := ImportResponse{
		TotalItems: len(pocketData.List),
		Errors:     []string{},
	}

	for _, item := range pocketData.List {
		err := h.importSingleItem(userID, item)
		if err != nil {
			response.Errors = append(response.Errors, fmt.Sprintf("Failed to import %s: %v", item.GivenURL, err))
			response.SkippedItems++
		} else {
			response.ImportedItems++
		}
	}

	response.Message = fmt.Sprintf("Import completed: %d imported, %d skipped",
		response.ImportedItems, response.SkippedItems)

	return response
}

// importSingleItem imports a single Pocket item
func (h *ImportHandler) importSingleItem(userID uint, item PocketItem) error {
	// Determine the URL to use
	url := item.ResolvedURL
	if url == "" {
		url = item.GivenURL
	}
	if url == "" {
		return fmt.Errorf("no URL found")
	}

	// Check if item already exists
	var existingItem models.SavedItem
	err := h.db.Where("user_id = ? AND url = ?", userID, url).First(&existingItem).Error
	if err == nil {
		// Item already exists, skip
		return nil
	}

	// Determine title
	title := item.ResolvedTitle
	if title == "" {
		title = item.GivenTitle
	}

	// Determine status
	status := "UNREAD"
	if item.Status == "1" { // Pocket status: 0=normal, 1=archived, 2=deleted
		status = "ARCHIVED"
	}

	// Determine if favorite
	isFavorite := item.Favorite == "1"

	// Parse timestamps
	var createdAt time.Time
	if timeAdded, err := strconv.ParseInt(item.TimeAdded, 10, 64); err == nil {
		createdAt = time.Unix(timeAdded, 0)
	} else {
		createdAt = time.Now()
	}

	// Create saved item
	savedItem := &models.SavedItem{
		UserID:     userID,
		URL:        url,
		Title:      title,
		Excerpt:    item.Excerpt,
		Status:     status,
		IsFavorite: isFavorite,
		CreatedAt:  createdAt,
		UpdatedAt:  time.Now(),
	}

	// Save to database
	if err := h.db.Create(savedItem).Error; err != nil {
		return err
	}

	// Extract metadata in background if title is empty
	if title == "" {
		h.metadataExtractor.ExtractMetadataAsync(savedItem)
	}

	return nil
}

// GetImportPage serves the import page
func (h *ImportHandler) GetImportPage(c *gin.Context) {
	c.HTML(http.StatusOK, "import.html", gin.H{
		"title": "Import Data - Save-It",
	})
}
