package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"save-it/middleware"
	"save-it/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// ImportHandler handles data import functionality
type ImportHandler struct {
	db                *gorm.DB
	metadataExtractor *MetadataExtractor
}

// NewImportHandler creates a new import handler
func NewImportHandler(db *gorm.DB) *ImportHandler {
	return &ImportHandler{
		db:                db,
		metadataExtractor: NewMetadataExtractor(db),
	}
}

// PocketItem represents a single item from Pocket export
type PocketItem struct {
	ItemID        string            `json:"item_id"`
	ResolvedID    string            `json:"resolved_id"`
	GivenURL      string            `json:"given_url"`
	ResolvedURL   string            `json:"resolved_url"`
	GivenTitle    string            `json:"given_title"`
	ResolvedTitle string            `json:"resolved_title"`
	Favorite      string            `json:"favorite"`
	Status        string            `json:"status"`
	TimeAdded     string            `json:"time_added"`
	TimeUpdated   string            `json:"time_updated"`
	TimeRead      string            `json:"time_read"`
	TimeFavorited string            `json:"time_favorited"`
	SortID        int               `json:"sort_id"`
	Excerpt       string            `json:"excerpt"`
	IsArticle     string            `json:"is_article"`
	HasImage      string            `json:"has_image"`
	HasVideo      string            `json:"has_video"`
	WordCount     string            `json:"word_count"`
	Tags          map[string]string `json:"tags,omitempty"`
}

// PocketExport represents the structure of a legacy Pocket export file
type PocketExport struct {
	Status   int                   `json:"status"`
	Complete int                   `json:"complete"`
	List     map[string]PocketItem `json:"list"`
	Error    interface{}           `json:"error"`
	Search   interface{}           `json:"search"`
	Since    int64                 `json:"since"`
}

// ModernPocketExport represents the structure of a modern Pocket GraphQL export
type ModernPocketExport struct {
	Edges []PocketEdge `json:"edges"`
}

// PocketEdge represents an edge in the GraphQL response
type PocketEdge struct {
	Cursor string     `json:"cursor"`
	Node   PocketNode `json:"node"`
}

// PocketNode represents a node in the GraphQL response
type PocketNode struct {
	CreatedAt          int64          `json:"_createdAt"`
	UpdatedAt          int64          `json:"_updatedAt"`
	Status             string         `json:"status"`
	IsFavorite         bool           `json:"isFavorite"`
	FavoritedAt        interface{}    `json:"favoritedAt"`
	IsArchived         bool           `json:"isArchived"`
	ArchivedAt         interface{}    `json:"archivedAt"`
	Tags               []PocketTag    `json:"tags"`
	Item               PocketItemNode `json:"item"`
	CreatedAtFormatted string         `json:"CreatedAtFormatted"`
}

// PocketTag represents a tag in the modern format
type PocketTag struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

// PocketItemNode represents an item in the modern format
type PocketItemNode struct {
	IsArticle  bool          `json:"isArticle"`
	HasImage   string        `json:"hasImage"`
	HasVideo   string        `json:"hasVideo"`
	TimeToRead interface{}   `json:"timeToRead"`
	ShareID    string        `json:"shareId"`
	ItemID     string        `json:"itemId"`
	GivenURL   string        `json:"givenUrl"`
	Preview    PocketPreview `json:"preview"`
}

// PocketPreview represents the preview data
type PocketPreview struct {
	ID            string         `json:"id"`
	Image         PocketImage    `json:"image"`
	Excerpt       string         `json:"excerpt"`
	Title         string         `json:"title"`
	Authors       []PocketAuthor `json:"authors"`
	Domain        PocketDomain   `json:"domain"`
	DatePublished string         `json:"datePublished"`
	URL           string         `json:"url"`
}

// PocketImage represents image data
type PocketImage struct {
	Caption      string              `json:"caption"`
	Credit       string              `json:"credit"`
	URL          string              `json:"url"`
	CachedImages []PocketCachedImage `json:"cachedImages"`
}

// PocketCachedImage represents cached image data
type PocketCachedImage struct {
	URL string `json:"url"`
	ID  string `json:"id"`
}

// PocketAuthor represents author data
type PocketAuthor struct {
	Name string `json:"name"`
}

// PocketDomain represents domain data
type PocketDomain struct {
	Name string `json:"name"`
}

// ImportResponse represents the response after importing data
type ImportResponse struct {
	Message       string   `json:"message"`
	TotalItems    int      `json:"total_items"`
	ImportedItems int      `json:"imported_items"`
	SkippedItems  int      `json:"skipped_items"`
	Errors        []string `json:"errors,omitempty"`
}

// ImportPocketData imports data from a Pocket JSON export
func (h *ImportHandler) ImportPocketData(c *gin.Context) {
	// Get user from context
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Authentication required",
		})
		return
	}

	// Get the uploaded file
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "No file uploaded or invalid file",
		})
		return
	}
	defer file.Close()

	// Check file size (limit to 50MB)
	if header.Size > 50*1024*1024 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "File too large (max 50MB)",
		})
		return
	}

	// First, read the beginning of the file to check for issues
	file.Seek(0, 0) // Reset file pointer
	firstBytes := make([]byte, 100)
	n, _ := file.Read(firstBytes)
	firstBytes = firstBytes[:n]

	// Check for BOM or other encoding issues
	if len(firstBytes) >= 3 && firstBytes[0] == 0xEF && firstBytes[1] == 0xBB && firstBytes[2] == 0xBF {
		// UTF-8 BOM detected, skip it
		file.Seek(3, 0)
	} else {
		file.Seek(0, 0)
	}

	// Try to parse as raw JSON to check structure
	var rawData map[string]interface{}
	decoder := json.NewDecoder(file)
	if err := decoder.Decode(&rawData); err != nil {
		// Try to provide more helpful error information
		errorMsg := "Invalid JSON format"
		maxLen := 50
		if len(firstBytes) < maxLen {
			maxLen = len(firstBytes)
		}
		debugInfo := fmt.Sprintf("First %d bytes: %q", maxLen, string(firstBytes[:maxLen]))

		if jsonErr, ok := err.(*json.SyntaxError); ok {
			errorMsg = fmt.Sprintf("JSON syntax error at byte offset %d", jsonErr.Offset)
		} else if jsonErr, ok := err.(*json.UnmarshalTypeError); ok {
			errorMsg = fmt.Sprintf("JSON type error: cannot unmarshal %s into %s", jsonErr.Value, jsonErr.Type)
		}

		c.JSON(http.StatusBadRequest, gin.H{
			"error":   errorMsg,
			"details": err.Error(),
			"debug":   debugInfo,
		})
		return
	}

	// Check if this is modern format (has edges) or legacy format (has status/list)
	if edges, hasEdges := rawData["edges"]; hasEdges {
		// Modern GraphQL format
		file.Seek(0, 0) // Reset file pointer
		var modernData ModernPocketExport
		decoder = json.NewDecoder(file)
		if err := decoder.Decode(&modernData); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "Failed to parse modern Pocket export format",
				"details": err.Error(),
			})
			return
		}

		// Validate edges array
		if edgesArray, ok := edges.([]interface{}); !ok || len(edgesArray) == 0 {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "Invalid modern Pocket export: empty or invalid edges array",
				"details": "The file appears to be a modern Pocket export but has no items",
			})
			return
		}

		// Process modern format
		response := h.processModernPocketImport(userID, &modernData)
		c.JSON(http.StatusOK, response)
		return
	}

	// Check for legacy format
	if _, hasStatus := rawData["status"]; !hasStatus {
		if _, hasList := rawData["list"]; !hasList {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "Unrecognized file format",
				"details": "This doesn't appear to be a Pocket export file. Expected 'edges' (modern) or 'status'+'list' (legacy) fields not found.",
			})
			return
		}
	}

	// Parse as legacy PocketExport
	file.Seek(0, 0) // Reset file pointer again
	var pocketData PocketExport
	decoder = json.NewDecoder(file)
	if err := decoder.Decode(&pocketData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Failed to parse legacy Pocket export format",
			"details": err.Error(),
		})
		return
	}

	// Validate that we have the expected structure
	if pocketData.List == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid legacy Pocket export format: missing 'list' field",
			"details": "The file doesn't appear to be a valid legacy Pocket export",
		})
		return
	}

	// Process legacy format
	response := h.processPocketImport(userID, &pocketData)

	c.JSON(http.StatusOK, response)
}

// processModernPocketImport processes modern Pocket GraphQL data and imports items
func (h *ImportHandler) processModernPocketImport(userID uint, modernData *ModernPocketExport) ImportResponse {
	response := ImportResponse{
		TotalItems: len(modernData.Edges),
		Errors:     []string{},
	}

	for _, edge := range modernData.Edges {
		err := h.importModernSingleItem(userID, edge.Node)
		if err != nil {
			response.Errors = append(response.Errors, fmt.Sprintf("Failed to import %s: %v", edge.Node.Item.GivenURL, err))
			response.SkippedItems++
		} else {
			response.ImportedItems++
		}
	}

	response.Message = fmt.Sprintf("Import completed: %d imported, %d skipped",
		response.ImportedItems, response.SkippedItems)

	return response
}

// processPocketImport processes the legacy Pocket data and imports items
func (h *ImportHandler) processPocketImport(userID uint, pocketData *PocketExport) ImportResponse {
	response := ImportResponse{
		TotalItems: len(pocketData.List),
		Errors:     []string{},
	}

	for _, item := range pocketData.List {
		err := h.importSingleItem(userID, item)
		if err != nil {
			response.Errors = append(response.Errors, fmt.Sprintf("Failed to import %s: %v", item.GivenURL, err))
			response.SkippedItems++
		} else {
			response.ImportedItems++
		}
	}

	response.Message = fmt.Sprintf("Import completed: %d imported, %d skipped",
		response.ImportedItems, response.SkippedItems)

	return response
}

// importSingleItem imports a single Pocket item
func (h *ImportHandler) importSingleItem(userID uint, item PocketItem) error {
	// Determine the URL to use
	url := item.ResolvedURL
	if url == "" {
		url = item.GivenURL
	}
	if url == "" {
		return fmt.Errorf("no URL found")
	}

	// Check if item already exists
	var existingItem models.SavedItem
	err := h.db.Where("user_id = ? AND url = ?", userID, url).First(&existingItem).Error
	if err == nil {
		// Item already exists, skip
		return nil
	}

	// Determine title
	title := item.ResolvedTitle
	if title == "" {
		title = item.GivenTitle
	}

	// Determine status
	status := "UNREAD"
	if item.Status == "1" { // Pocket status: 0=normal, 1=archived, 2=deleted
		status = "ARCHIVED"
	}

	// Determine if favorite
	isFavorite := item.Favorite == "1"

	// Parse timestamps
	var createdAt time.Time
	if timeAdded, err := strconv.ParseInt(item.TimeAdded, 10, 64); err == nil {
		createdAt = time.Unix(timeAdded, 0)
	} else {
		createdAt = time.Now()
	}

	// Create saved item
	savedItem := &models.SavedItem{
		UserID:     userID,
		URL:        url,
		Title:      title,
		Excerpt:    item.Excerpt,
		Status:     status,
		IsFavorite: isFavorite,
		CreatedAt:  createdAt,
		UpdatedAt:  time.Now(),
	}

	// Save to database
	if err := h.db.Create(savedItem).Error; err != nil {
		return err
	}

	// Extract metadata in background if title is empty
	if title == "" {
		h.metadataExtractor.ExtractMetadataAsync(savedItem)
	}

	return nil
}

// importModernSingleItem imports a single modern Pocket item
func (h *ImportHandler) importModernSingleItem(userID uint, node PocketNode) error {
	// Get URL from the item
	url := node.Item.GivenURL
	if url == "" {
		return fmt.Errorf("no URL found")
	}

	// Check if item already exists
	var existingItem models.SavedItem
	err := h.db.Where("user_id = ? AND url = ?", userID, url).First(&existingItem).Error
	if err == nil {
		// Item already exists, skip
		return nil
	}

	// Get title from preview
	title := node.Item.Preview.Title
	if title == "" {
		title = url // Fallback to URL if no title
	}

	// Determine status
	status := "UNREAD"
	if node.Status == "ARCHIVED" || node.IsArchived {
		status = "ARCHIVED"
	}

	// Get image URL
	imageURL := ""
	if node.Item.Preview.Image.URL != "" {
		imageURL = node.Item.Preview.Image.URL
	}

	// Parse creation timestamp
	var createdAt time.Time
	if node.CreatedAt > 0 {
		createdAt = time.Unix(node.CreatedAt, 0)
	} else {
		createdAt = time.Now()
	}

	// Create saved item
	savedItem := &models.SavedItem{
		UserID:     userID,
		URL:        url,
		Title:      title,
		Excerpt:    node.Item.Preview.Excerpt,
		ImageURL:   imageURL,
		Status:     status,
		IsFavorite: node.IsFavorite,
		CreatedAt:  createdAt,
		UpdatedAt:  time.Now(),
	}

	// Save to database
	if err := h.db.Create(savedItem).Error; err != nil {
		return err
	}

	// Extract metadata in background if title is empty or generic
	if title == "" || title == url {
		h.metadataExtractor.ExtractMetadataAsync(savedItem)
	}

	return nil
}

// GetImportPage serves the import page
func (h *ImportHandler) GetImportPage(c *gin.Context) {
	c.HTML(http.StatusOK, "import.html", gin.H{
		"title": "Import Data - Save-It",
	})
}
