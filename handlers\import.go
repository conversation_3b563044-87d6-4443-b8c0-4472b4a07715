package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"save-it/middleware"
	"save-it/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// ImportHandler handles data import functionality
type ImportHandler struct {
	db                *gorm.DB
	metadataExtractor *MetadataExtractor
}

// NewImportHandler creates a new import handler
func NewImportHandler(db *gorm.DB) *ImportHandler {
	return &ImportHandler{
		db:                db,
		metadataExtractor: NewMetadataExtractor(db),
	}
}

// PocketItem represents a single item from Pocket export
type PocketItem struct {
	ItemID        string            `json:"item_id"`
	ResolvedID    string            `json:"resolved_id"`
	GivenURL      string            `json:"given_url"`
	ResolvedURL   string            `json:"resolved_url"`
	GivenTitle    string            `json:"given_title"`
	ResolvedTitle string            `json:"resolved_title"`
	Favorite      string            `json:"favorite"`
	Status        string            `json:"status"`
	TimeAdded     string            `json:"time_added"`
	TimeUpdated   string            `json:"time_updated"`
	TimeRead      string            `json:"time_read"`
	TimeFavorited string            `json:"time_favorited"`
	SortID        int               `json:"sort_id"`
	Excerpt       string            `json:"excerpt"`
	IsArticle     string            `json:"is_article"`
	HasImage      string            `json:"has_image"`
	HasVideo      string            `json:"has_video"`
	WordCount     string            `json:"word_count"`
	Tags          map[string]string `json:"tags,omitempty"`
}

// PocketExport represents the structure of a Pocket export file
type PocketExport struct {
	Status   int                   `json:"status"`
	Complete int                   `json:"complete"`
	List     map[string]PocketItem `json:"list"`
	Error    interface{}           `json:"error"`
	Search   interface{}           `json:"search"`
	Since    int64                 `json:"since"`
}

// ImportResponse represents the response after importing data
type ImportResponse struct {
	Message       string `json:"message"`
	TotalItems    int    `json:"total_items"`
	ImportedItems int    `json:"imported_items"`
	SkippedItems  int    `json:"skipped_items"`
	Errors        []string `json:"errors,omitempty"`
}

// ImportPocketData imports data from a Pocket JSON export
func (h *ImportHandler) ImportPocketData(c *gin.Context) {
	// Get user from context
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Authentication required",
		})
		return
	}

	// Get the uploaded file
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "No file uploaded or invalid file",
		})
		return
	}
	defer file.Close()

	// Check file size (limit to 10MB)
	if header.Size > 10*1024*1024 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "File too large (max 10MB)",
		})
		return
	}

	// Parse JSON
	var pocketData PocketExport
	decoder := json.NewDecoder(file)
	if err := decoder.Decode(&pocketData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid JSON format",
			"details": err.Error(),
		})
		return
	}

	// Process the import
	response := h.processPocketImport(userID, &pocketData)

	c.JSON(http.StatusOK, response)
}

// processPocketImport processes the Pocket data and imports items
func (h *ImportHandler) processPocketImport(userID uint, pocketData *PocketExport) ImportResponse {
	response := ImportResponse{
		TotalItems: len(pocketData.List),
		Errors:     []string{},
	}

	for _, item := range pocketData.List {
		err := h.importSingleItem(userID, item)
		if err != nil {
			response.Errors = append(response.Errors, fmt.Sprintf("Failed to import %s: %v", item.GivenURL, err))
			response.SkippedItems++
		} else {
			response.ImportedItems++
		}
	}

	response.Message = fmt.Sprintf("Import completed: %d imported, %d skipped", 
		response.ImportedItems, response.SkippedItems)

	return response
}

// importSingleItem imports a single Pocket item
func (h *ImportHandler) importSingleItem(userID uint, item PocketItem) error {
	// Determine the URL to use
	url := item.ResolvedURL
	if url == "" {
		url = item.GivenURL
	}
	if url == "" {
		return fmt.Errorf("no URL found")
	}

	// Check if item already exists
	var existingItem models.SavedItem
	err := h.db.Where("user_id = ? AND url = ?", userID, url).First(&existingItem).Error
	if err == nil {
		// Item already exists, skip
		return nil
	}

	// Determine title
	title := item.ResolvedTitle
	if title == "" {
		title = item.GivenTitle
	}

	// Determine status
	status := "UNREAD"
	if item.Status == "1" { // Pocket status: 0=normal, 1=archived, 2=deleted
		status = "ARCHIVED"
	}

	// Determine if favorite
	isFavorite := item.Favorite == "1"

	// Parse timestamps
	var createdAt time.Time
	if timeAdded, err := strconv.ParseInt(item.TimeAdded, 10, 64); err == nil {
		createdAt = time.Unix(timeAdded, 0)
	} else {
		createdAt = time.Now()
	}

	// Create saved item
	savedItem := &models.SavedItem{
		UserID:     userID,
		URL:        url,
		Title:      title,
		Excerpt:    item.Excerpt,
		Status:     status,
		IsFavorite: isFavorite,
		CreatedAt:  createdAt,
		UpdatedAt:  time.Now(),
	}

	// Save to database
	if err := h.db.Create(savedItem).Error; err != nil {
		return err
	}

	// Extract metadata in background if title is empty
	if title == "" {
		h.metadataExtractor.ExtractMetadataAsync(savedItem)
	}

	return nil
}

// GetImportPage serves the import page
func (h *ImportHandler) GetImportPage(c *gin.Context) {
	c.HTML(http.StatusOK, "import.html", gin.H{
		"title": "Import Data - Save-It",
	})
}
