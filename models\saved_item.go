package models

import (
	"time"

	"gorm.io/gorm"
)

// SavedItem represents a saved URL/bookmark
type SavedItem struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	UserID     uint      `json:"user_id" gorm:"not null;index"`
	URL        string    `json:"url" gorm:"not null"`
	Title      string    `json:"title"`
	Excerpt    string    `json:"excerpt"`
	ImageURL   string    `json:"image_url"`
	Status     string    `json:"status" gorm:"default:'UNREAD'"` // UNREAD, ARCHIVED
	IsFavorite bool      `json:"is_favorite" gorm:"default:false"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`

	// Relationships
	User User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// SaveItemRequest represents the data needed to save a new item
type SaveItemRequest struct {
	URL string `json:"url" binding:"required,url"`
}

// UpdateItemRequest represents the data that can be updated for a saved item
type UpdateItemRequest struct {
	Title          *string `json:"title,omitempty"`
	Status         *string `json:"status,omitempty"`
	IsFavorite     *bool   `json:"is_favorite,omitempty"`
	ToggleFavorite bool    `json:"toggle_favorite,omitempty"`
}

// SavedItemResponse represents the saved item data returned in API responses
type SavedItemResponse struct {
	ID         uint      `json:"id"`
	URL        string    `json:"url"`
	Title      string    `json:"title"`
	Excerpt    string    `json:"excerpt"`
	ImageURL   string    `json:"image_url"`
	Status     string    `json:"status"`
	IsFavorite bool      `json:"is_favorite"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// SavedItemsResponse represents a list of saved items with metadata
type SavedItemsResponse struct {
	Items []SavedItemResponse `json:"items"`
	Total int64               `json:"total"`
	Page  int                 `json:"page"`
	Limit int                 `json:"limit"`
}

// BeforeCreate is a GORM hook that runs before creating a saved item
func (s *SavedItem) BeforeCreate(tx *gorm.DB) error {
	s.CreatedAt = time.Now()
	s.UpdatedAt = time.Now()
	if s.Status == "" {
		s.Status = "UNREAD"
	}
	return nil
}

// BeforeUpdate is a GORM hook that runs before updating a saved item
func (s *SavedItem) BeforeUpdate(tx *gorm.DB) error {
	s.UpdatedAt = time.Now()
	return nil
}

// ToResponse converts a SavedItem to SavedItemResponse
func (s *SavedItem) ToResponse() SavedItemResponse {
	return SavedItemResponse{
		ID:         s.ID,
		URL:        s.URL,
		Title:      s.Title,
		Excerpt:    s.Excerpt,
		ImageURL:   s.ImageURL,
		Status:     s.Status,
		IsFavorite: s.IsFavorite,
		CreatedAt:  s.CreatedAt,
		UpdatedAt:  s.UpdatedAt,
	}
}

// CreateSavedItem creates a new saved item for a user
func CreateSavedItem(db *gorm.DB, userID uint, request SaveItemRequest) (*SavedItem, error) {
	// Check if URL already exists for this user
	var existingItem SavedItem
	err := db.Where("user_id = ? AND url = ?", userID, request.URL).First(&existingItem).Error
	if err == nil {
		// URL already exists, return the existing item
		return &existingItem, nil
	}

	savedItem := &SavedItem{
		UserID: userID,
		URL:    request.URL,
		Status: "UNREAD",
	}

	if err := db.Create(savedItem).Error; err != nil {
		return nil, err
	}

	return savedItem, nil
}

// GetSavedItemsByUser retrieves all saved items for a user with optional filtering and sorting
func GetSavedItemsByUser(db *gorm.DB, userID uint, query, status string, favorite *bool, sortBy string, page, limit int) ([]SavedItem, int64, error) {
	var items []SavedItem
	var total int64

	// Build the query
	dbQuery := db.Where("user_id = ?", userID)

	// Apply filters
	if query != "" {
		dbQuery = dbQuery.Where("title LIKE ? OR url LIKE ? OR excerpt LIKE ?",
			"%"+query+"%", "%"+query+"%", "%"+query+"%")
	}

	if status != "" {
		dbQuery = dbQuery.Where("status = ?", status)
	}

	if favorite != nil {
		dbQuery = dbQuery.Where("is_favorite = ?", *favorite)
	}

	// Count total items
	if err := dbQuery.Model(&SavedItem{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Determine sort order
	var orderBy string
	switch sortBy {
	case "created_at_asc":
		orderBy = "created_at ASC"
	case "created_at_desc":
		orderBy = "created_at DESC"
	case "title_asc":
		orderBy = "title ASC"
	case "title_desc":
		orderBy = "title DESC"
	case "updated_at_asc":
		orderBy = "updated_at ASC"
	case "updated_at_desc":
		orderBy = "updated_at DESC"
	default:
		orderBy = "created_at DESC" // Default sort
	}

	// Apply pagination and ordering
	offset := (page - 1) * limit
	if err := dbQuery.Order(orderBy).Offset(offset).Limit(limit).Find(&items).Error; err != nil {
		return nil, 0, err
	}

	return items, total, nil
}

// GetSavedItemByID retrieves a saved item by ID and user ID
func GetSavedItemByID(db *gorm.DB, id, userID uint) (*SavedItem, error) {
	var item SavedItem
	err := db.Where("id = ? AND user_id = ?", id, userID).First(&item).Error
	if err != nil {
		return nil, err
	}
	return &item, nil
}

// UpdateSavedItem updates a saved item
func UpdateSavedItem(db *gorm.DB, id, userID uint, request UpdateItemRequest) (*SavedItem, error) {
	item, err := GetSavedItemByID(db, id, userID)
	if err != nil {
		return nil, err
	}

	// Apply updates
	if request.Title != nil {
		item.Title = *request.Title
	}

	if request.Status != nil {
		item.Status = *request.Status
	}

	if request.IsFavorite != nil {
		item.IsFavorite = *request.IsFavorite
	}

	if request.ToggleFavorite {
		item.IsFavorite = !item.IsFavorite
	}

	if err := db.Save(item).Error; err != nil {
		return nil, err
	}

	return item, nil
}

// DeleteSavedItem deletes a saved item
func DeleteSavedItem(db *gorm.DB, id, userID uint) error {
	result := db.Where("id = ? AND user_id = ?", id, userID).Delete(&SavedItem{})
	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}

	return nil
}

// SetMetadata updates the metadata fields of a saved item
func (s *SavedItem) SetMetadata(title, excerpt, imageURL string) {
	if title != "" {
		s.Title = title
	}
	if excerpt != "" {
		s.Excerpt = excerpt
	}
	if imageURL != "" {
		s.ImageURL = imageURL
	}
}
