# Save-It Application - Feature Specification

## Overview
Save-It is a lightweight, secure web application similar to Pocket that allows users to save URLs, manage their saved content, and access it from both desktop and mobile browsers.

## Core Features

### 1. User Authentication & Security
- **User Registration**: Create new accounts with username and password
- **User Login**: Secure authentication system
- **JWT Authentication**: JSON Web Tokens for API security
- **Password Security**: bcrypt hashing for password storage
- **Session Management**: Secure token-based sessions
- **Input Validation**: Comprehensive validation and sanitization
- **HTTPS Support**: SSL/TLS encryption using provided certificates

### 2. URL Saving & Management
- **Quick Save**: Save URLs with a single click via bookmarklet
- **Metadata Extraction**: Automatically fetch page title, description, and images
- **URL Validation**: Ensure saved URLs are valid and accessible
- **Bulk Operations**: Select and manage multiple saved items
- **Status Tracking**: Mark items as read/unread, archived, or favorite
- **Duplicate Detection**: Prevent saving the same URL multiple times

### 3. Web Interface
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **Clean UI**: Intuitive and user-friendly interface
- **Theme Support**: Light and dark theme options
- **Navigation**: Easy-to-use menu and navigation system
- **Search Interface**: Built-in search and filtering capabilities
- **Item Management**: View, edit, and delete saved items

### 4. Search & Organization
- **Text Search**: Search by title, URL, or description
- **Filter Options**: Filter by status, favorites, date ranges
- **Sorting**: Sort by date saved, title, or custom criteria
- **Tag System**: Organize items with custom tags (optional)
- **Advanced Search**: Combine multiple search criteria
- **Quick Filters**: One-click filters for common searches

### 5. Bookmarklet Integration
- **Auto-Generation**: Generate personalized bookmarklet with user's token
- **Cross-Browser**: Works with all major browsers
- **Security Warning**: Clear warnings about token security
- **Easy Installation**: Simple drag-and-drop installation
- **Current Page Detection**: Automatically captures current page URL

### 6. Data Management
- **Import Support**: Import existing data from Pocket (pocket.json)
- **Export Functionality**: Export saved items for backup
- **Data Backup**: Regular backup capabilities
- **Data Migration**: Easy migration between instances
- **Data Integrity**: Ensure data consistency and reliability

## API Endpoints

### Authentication Endpoints
- `POST /api/register` - User registration
- `POST /api/login` - User login
- `GET /api/user` - Get current user information

### Content Management Endpoints
- `POST /api/save` - Save a new URL (authenticated)
- `GET /api/saves` - Retrieve all saved URLs for user (authenticated)
- `DELETE /api/save/{id}` - Delete a saved URL (authenticated)
- `PUT /api/save/{id}` - Update saved URL metadata (authenticated)

### Utility Endpoints
- `GET /api/bookmarklet` - Generate user's bookmarklet
- `POST /api/import` - Import data from external sources
- `GET /api/export` - Export user's saved data

## Technical Specifications

### Backend Technology
- **Framework**: Gin (Go web framework)
- **Database**: SQLite (with PostgreSQL option)
- **ORM**: GORM for database operations
- **Authentication**: JWT (JSON Web Tokens)
- **Web Scraping**: goquery for metadata extraction
- **Security**: bcrypt for password hashing

### Frontend Technology
- **CSS Framework**: Bootstrap or Tailwind CSS
- **JavaScript**: Vanilla JS for interactivity
- **Templates**: Go HTML templates
- **Responsive Design**: Mobile-first approach
- **Theme System**: CSS variables for theme switching

### Database Schema
- **Users Table**: id, username, password_hash, created_at, updated_at
- **SavedItems Table**: id, user_id, url, title, excerpt, image_url, status, is_favorite, created_at, updated_at
- **Tags Table**: id, name, user_id (optional)
- **SavedItemTags Table**: saved_item_id, tag_id (optional)

### Security Features
- **HTTPS Encryption**: SSL/TLS support
- **Password Hashing**: bcrypt with salt
- **JWT Security**: Secure token generation and validation
- **Input Sanitization**: XSS and injection prevention
- **Rate Limiting**: API endpoint protection
- **CORS Configuration**: Cross-origin request handling

## Optional Advanced Features

### Enhanced Organization
- **Notes & Comments**: Add personal notes to saved items
- **Reading Time Estimation**: Calculate estimated reading time
- **Archive System**: Archive old or completed items
- **Folder Organization**: Organize items into custom folders

### Integration Features
- **RSS Feed Support**: Subscribe to and save from RSS feeds
- **Browser Extension**: Dedicated browser extension (future)
- **Mobile App**: Native mobile applications (future)
- **API Access**: Third-party API access for integrations

### User Experience
- **Keyboard Shortcuts**: Quick actions via keyboard
- **Bulk Actions**: Select and perform actions on multiple items
- **Undo Functionality**: Undo recent actions
- **Auto-Save**: Automatically save drafts and changes

### Analytics & Insights
- **Reading Statistics**: Track reading habits and statistics
- **Popular Items**: Identify most accessed content
- **Usage Analytics**: Personal usage insights and trends
- **Content Recommendations**: Suggest related content

## Deployment Configuration

### Server Requirements
- **Port**: localhost:9191 (configurable)
- **Certificates**: SSL certificates provided in /cert folder
- **Database**: SQLite file-based database
- **Environment**: Local development and production ready

### Installation Requirements
- **Go Version**: 1.20 or later
- **Dependencies**: Managed via go.mod
- **Database Setup**: Automatic SQLite initialization
- **Configuration**: Environment variables for sensitive data

## Security Considerations

### Bookmarklet Security
- **Token Exposure Warning**: Clear warnings about JWT token in bookmarklet
- **Trusted Device Recommendation**: Advise use only on trusted devices
- **Token Rotation**: Regular token refresh capabilities
- **Secure Storage**: Local storage security best practices

### Data Protection
- **Input Validation**: Comprehensive server-side validation
- **SQL Injection Prevention**: Parameterized queries
- **XSS Protection**: Output encoding and sanitization
- **CSRF Protection**: Cross-site request forgery prevention
