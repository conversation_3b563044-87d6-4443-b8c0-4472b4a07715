/* Save-It Application Styles */

:root {
    /* Light theme colors */
    --bg-color: #ffffff;
    --text-color: #333333;
    --card-bg: #f8f9fa;
    --border-color: #dee2e6;
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
}

[data-theme="dark"] {
    /* Dark theme colors */
    --bg-color: #1a1a1a;
    --text-color: #ffffff;
    --card-bg: #2d2d2d;
    --border-color: #404040;
    --primary-color: #4dabf7;
    --secondary-color: #adb5bd;
}

body {
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}

.card {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-color);
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

.card-title {
    color: var(--text-color);
}

.card-text {
    color: var(--text-color);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.theme-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    background: var(--card-bg);
    border: 2px solid var(--border-color);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    color: var(--text-color);
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
    border-color: var(--primary-color);
}

.theme-toggle:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.25);
}

.saved-item {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: var(--card-bg);
    transition: all 0.3s ease;
    /* Ensure content doesn't overflow */
    overflow: hidden;
    word-wrap: break-word;
}

.saved-item:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.saved-item-thumbnail {
    width: 120px;
    height: 80px;
    object-fit: cover;
    border-radius: 6px;
    flex-shrink: 0;
}

.saved-item-content {
    flex: 1;
    min-width: 0; /* Allow flex items to shrink below content size */
    overflow: hidden; /* Prevent content from overflowing */
    word-wrap: break-word;
}

.view-toggle {
    display: flex;
    gap: 0.5rem;
}

.view-toggle .btn {
    padding: 0.375rem 0.75rem;
}

.view-toggle .btn.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* Compact view styles */
.saved-item.compact {
    padding: 0.75rem;
    margin-bottom: 0.5rem;
}

.saved-item.compact .saved-item-thumbnail {
    width: 60px;
    height: 40px;
}

.saved-item.compact .saved-item-title {
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.saved-item.compact .saved-item-excerpt {
    display: none;
}

/* List view styles */
.saved-item.list {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
}

.saved-item.list .saved-item-thumbnail {
    margin-top: 0.25rem;
}

.saved-item-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    /* Handle long titles */
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    line-height: 1.4;
}

.saved-item-title a {
    /* Ensure links break properly */
    word-break: break-word;
    overflow-wrap: break-word;
}

.saved-item-url {
    color: var(--secondary-color);
    font-size: 0.9rem;
    text-decoration: none;
    /* Handle long URLs */
    word-break: break-all;
    overflow-wrap: break-word;
    line-height: 1.3;
    display: block;
    max-width: 100%;
}

.saved-item-excerpt {
    margin-top: 0.5rem;
    color: var(--text-color);
    opacity: 0.8;
    /* Handle long words in descriptions */
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    line-height: 1.5;
}

.saved-item-meta {
    font-size: 0.8rem;
    color: var(--secondary-color);
    margin-top: 0.5rem;
    /* Handle long meta text */
    word-wrap: break-word;
    overflow-wrap: break-word;
    line-height: 1.4;
}

.search-container {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 1px solid var(--border-color);
}

.btn-favorite {
    color: #ffc107;
    border: none;
    background: none;
    font-size: 1.2rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
}

.btn-favorite:hover {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.btn-favorite.active {
    color: #ff6b35;
}

.btn-favorite.active:hover {
    background-color: rgba(255, 107, 53, 0.1);
    color: #ff6b35;
}

/* Ensure SVG icons inherit button color */
.btn svg {
    fill: currentColor;
}

.btn-favorite svg {
    width: 16px;
    height: 16px;
}

.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.loading {
    text-align: center;
    padding: 2rem;
    color: var(--secondary-color);
}

/* Text overflow utility classes */
.text-break {
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    word-break: break-word !important;
}

.text-break-all {
    word-break: break-all !important;
    overflow-wrap: break-word !important;
}

.text-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .theme-toggle {
        bottom: 10px;
        right: 10px;
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .container {
        padding-left: 15px;
        padding-right: 15px;
    }

    .saved-item {
        padding: 0.75rem;
    }

    /* Enhanced mobile text handling */
    .saved-item-title {
        font-size: 1.1rem;
        line-height: 1.3;
    }

    .saved-item-url {
        font-size: 0.8rem;
        word-break: break-all;
    }

    .saved-item-excerpt {
        font-size: 0.9rem;
        line-height: 1.4;
    }

    /* Ensure list view works well on mobile */
    .saved-item.list {
        flex-direction: column;
        gap: 0.75rem;
    }

    .saved-item.list .saved-item-thumbnail {
        width: 100%;
        height: 120px;
        margin-top: 0;
    }
}

/* Form styles */
.form-control, .form-select {
    background-color: var(--bg-color);
    border-color: var(--border-color);
    color: var(--text-color);
}

.form-control:focus, .form-select:focus {
    background-color: var(--bg-color);
    border-color: var(--primary-color);
    color: var(--text-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Dark theme specific overrides */
[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .navbar-dark {
    background-color: #1a1a1a !important;
}

[data-theme="dark"] .btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

[data-theme="dark"] .btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

[data-theme="dark"] .card-body {
    color: var(--text-color);
}

[data-theme="dark"] .card-title,
[data-theme="dark"] .card-text {
    color: var(--text-color) !important;
}

[data-theme="dark"] .lead {
    color: var(--text-color);
}

[data-theme="dark"] .display-4 {
    color: var(--text-color);
}

[data-theme="dark"] .saved-item {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .saved-item-title a {
    color: var(--primary-color);
}

[data-theme="dark"] .saved-item-url {
    color: var(--secondary-color);
}

[data-theme="dark"] .saved-item-excerpt {
    color: var(--text-color);
}

[data-theme="dark"] .saved-item-meta {
    color: var(--secondary-color);
}

[data-theme="dark"] .search-container {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .text-muted {
    color: var(--secondary-color) !important;
}

/* Dark theme navbar improvements */
[data-theme="dark"] .navbar {
    background-color: var(--card-bg) !important;
    border-bottom: 1px solid var(--border-color);
}

[data-theme="dark"] .navbar-brand,
[data-theme="dark"] .nav-link {
    color: var(--text-color) !important;
}

[data-theme="dark"] .nav-link:hover,
[data-theme="dark"] .nav-link:focus {
    color: var(--primary-color) !important;
}

[data-theme="dark"] .dropdown-menu {
    background-color: var(--card-bg);
    border-color: var(--border-color);
}

[data-theme="dark"] .dropdown-item {
    color: var(--text-color);
}

[data-theme="dark"] .dropdown-item:hover,
[data-theme="dark"] .dropdown-item:focus {
    background-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .dropdown-divider {
    border-color: var(--border-color);
}

/* Dark theme modal improvements */
[data-theme="dark"] .modal-content {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .modal-header {
    border-bottom-color: var(--border-color);
}

[data-theme="dark"] .modal-footer {
    border-top-color: var(--border-color);
}

[data-theme="dark"] .modal-title {
    color: var(--text-color);
}

[data-theme="dark"] .btn-close {
    filter: invert(1);
}

/* Dark theme button improvements */
[data-theme="dark"] .btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--bg-color);
}

[data-theme="dark"] .btn-outline-secondary {
    color: var(--secondary-color);
    border-color: var(--secondary-color);
}

[data-theme="dark"] .btn-outline-secondary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--bg-color);
}

/* Dark theme alert improvements */
[data-theme="dark"] .alert-danger {
    background-color: #2d1b1b;
    border-color: #5c2e2e;
    color: #f8d7da;
}

[data-theme="dark"] .alert-success {
    background-color: #1b2d1b;
    border-color: #2e5c2e;
    color: #d1e7dd;
}

[data-theme="dark"] .alert-info {
    background-color: #1b252d;
    border-color: #2e4a5c;
    color: #b3d4fc;
}

/* Dark theme badge improvements */
[data-theme="dark"] .badge {
    color: var(--bg-color) !important;
}

[data-theme="dark"] .bg-secondary {
    background-color: var(--secondary-color) !important;
}

/* Dark theme pagination improvements */
[data-theme="dark"] .pagination .page-link {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .pagination .page-link:hover {
    background-color: var(--border-color);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}
