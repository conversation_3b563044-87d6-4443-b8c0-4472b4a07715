/* Save-It Application Styles */

:root {
    /* Light theme colors */
    --bg-color: #ffffff;
    --text-color: #333333;
    --card-bg: #f8f9fa;
    --border-color: #dee2e6;
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
}

[data-theme="dark"] {
    /* Dark theme colors */
    --bg-color: #1a1a1a;
    --text-color: #ffffff;
    --card-bg: #2d2d2d;
    --border-color: #404040;
    --primary-color: #4dabf7;
    --secondary-color: #adb5bd;
}

body {
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}

.card {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-color);
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

.card-title {
    color: var(--text-color);
}

.card-text {
    color: var(--text-color);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    background: var(--primary-color);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
}

.theme-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
}

.saved-item {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: var(--card-bg);
    transition: all 0.3s ease;
}

.saved-item:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.saved-item-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.saved-item-url {
    color: var(--secondary-color);
    font-size: 0.9rem;
    text-decoration: none;
}

.saved-item-excerpt {
    margin-top: 0.5rem;
    color: var(--text-color);
    opacity: 0.8;
}

.saved-item-meta {
    font-size: 0.8rem;
    color: var(--secondary-color);
    margin-top: 0.5rem;
}

.search-container {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 1px solid var(--border-color);
}

.btn-favorite {
    color: #ffc107;
    border: none;
    background: none;
    font-size: 1.2rem;
}

.btn-favorite.active {
    color: #ff6b35;
}

.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.loading {
    text-align: center;
    padding: 2rem;
    color: var(--secondary-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .theme-toggle {
        top: 10px;
        right: 10px;
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .container {
        padding-left: 15px;
        padding-right: 15px;
    }

    .saved-item {
        padding: 0.75rem;
    }
}

/* Form styles */
.form-control, .form-select {
    background-color: var(--bg-color);
    border-color: var(--border-color);
    color: var(--text-color);
}

.form-control:focus, .form-select:focus {
    background-color: var(--bg-color);
    border-color: var(--primary-color);
    color: var(--text-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Dark theme specific overrides */
[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .navbar-dark {
    background-color: #1a1a1a !important;
}

[data-theme="dark"] .btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

[data-theme="dark"] .btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

[data-theme="dark"] .card-body {
    color: var(--text-color);
}

[data-theme="dark"] .card-title,
[data-theme="dark"] .card-text {
    color: var(--text-color) !important;
}

[data-theme="dark"] .lead {
    color: var(--text-color);
}

[data-theme="dark"] .display-4 {
    color: var(--text-color);
}
