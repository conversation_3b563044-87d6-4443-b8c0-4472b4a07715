<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Saves - Save-It</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <strong>Save-It</strong>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/saves">My Saves</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/import">Import</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <span id="username-display">User</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/bookmarklet">Bookmarklet</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="logout-btn">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Search and Filter Section -->
        <div class="search-container">
            <div class="row">
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" class="form-control" id="search-input" placeholder="Search your saves...">
                        <button class="btn btn-outline-secondary" type="button" id="search-btn">
                            🔍
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="status-filter">
                        <option value="">All Status</option>
                        <option value="UNREAD">Unread</option>
                        <option value="ARCHIVED">Archived</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="favorite-filter">
                        <option value="">All Items</option>
                        <option value="true">Favorites Only</option>
                        <option value="false">Non-Favorites</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-4">
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUrlModal">
                        ➕ Add URL
                    </button>
                </div>
                <div class="col-md-4 text-center">
                    <div class="view-toggle">
                        <button class="btn btn-outline-secondary active" id="view-list" data-view="list">
                            📋 List
                        </button>
                        <button class="btn btn-outline-secondary" id="view-compact" data-view="compact">
                            📄 Compact
                        </button>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex justify-content-end align-items-center gap-2">
                        <select class="form-select form-select-sm" id="sort-select" style="width: auto;">
                            <option value="created_at_desc">Originally Saved (Newest)</option>
                            <option value="created_at_asc">Originally Saved (Oldest)</option>
                            <option value="updated_at_desc" selected>Recently Added/Updated</option>
                            <option value="updated_at_asc">Earliest Added/Updated</option>
                            <option value="title_asc">Title A-Z</option>
                            <option value="title_desc">Title Z-A</option>
                        </select>
                        <small class="text-muted" id="results-count">Loading...</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading State -->
        <div id="loading" class="loading">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p>Loading your saves...</p>
        </div>

        <!-- Saves Container -->
        <div id="saves-container">
            <!-- Saved items will be loaded here -->
        </div>

        <!-- Pagination -->
        <nav aria-label="Saves pagination" id="pagination-nav" class="d-none mt-4">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <small class="text-muted" id="pagination-info">
                        Showing 1-20 of 100 items
                    </small>
                </div>
                <ul class="pagination mb-0" id="pagination">
                    <!-- Pagination will be generated here -->
                </ul>
                <div>
                    <select class="form-select form-select-sm" id="items-per-page" style="width: auto;">
                        <option value="10">10 per page</option>
                        <option value="20" selected>20 per page</option>
                        <option value="50">50 per page</option>
                        <option value="100">100 per page</option>
                    </select>
                </div>
            </div>
        </nav>
    </div>

    <!-- Add URL Modal -->
    <div class="modal fade" id="addUrlModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New URL</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="add-url-error" class="alert alert-danger d-none"></div>
                    <form id="add-url-form">
                        <div class="mb-3">
                            <label for="url-input" class="form-label">URL</label>
                            <input type="url" class="form-control" id="url-input" name="url"
                                   placeholder="https://example.com" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="save-url-btn">
                        <span class="spinner-border spinner-border-sm d-none" id="save-url-spinner"></span>
                        Save URL
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/app.js"></script>
    <script>
        // Global variables for pagination and sorting
        let currentPage = 1;
        let currentLimit = 20;
        let currentSort = 'updated_at_desc'; // Default to recently added/updated
        let currentView = localStorage.getItem('saves_view') || 'list';

        // Make variables available globally for SearchManager
        window.currentPage = currentPage;
        window.currentLimit = currentLimit;
        window.currentSort = currentSort;

        // Check authentication on page load
        document.addEventListener('DOMContentLoaded', function() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || '{}');

            if (!token) {
                window.location.href = '/login';
                return;
            }

            // Set username in navbar
            if (user.username) {
                document.getElementById('username-display').textContent = user.username;
            }

            // Load saves
            loadSaves();

            // Set up event listeners
            setupEventListeners();
        });

        function setupEventListeners() {
            // Logout
            document.getElementById('logout-btn').addEventListener('click', function(e) {
                e.preventDefault();
                localStorage.removeItem('auth_token');
                localStorage.removeItem('user');
                window.location.href = '/login';
            });

            // Add URL form
            document.getElementById('save-url-btn').addEventListener('click', saveNewUrl);

            // View toggle
            document.querySelectorAll('.view-toggle .btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.view-toggle .btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    const view = this.dataset.view;
                    localStorage.setItem('saves_view', view);
                    updateItemsView(view);
                });
            });

            // Sort change
            document.getElementById('sort-select').addEventListener('change', function() {
                currentSort = this.value;
                window.currentSort = currentSort;
                currentPage = 1;
                window.currentPage = currentPage;
                loadSaves();
            });

            // Items per page change
            document.getElementById('items-per-page').addEventListener('change', function() {
                currentLimit = parseInt(this.value);
                window.currentLimit = currentLimit;
                currentPage = 1;
                window.currentPage = currentPage;
                loadSaves();
            });

            // Search functionality is handled by SearchManager in app.js
        }

        async function loadSaves() {
            const loading = document.getElementById('loading');
            const container = document.getElementById('saves-container');
            const resultsCount = document.getElementById('results-count');

            try {
                loading.style.display = 'block';
                container.innerHTML = '';

                // Build query parameters
                const params = new URLSearchParams();
                params.append('page', currentPage.toString());
                params.append('limit', currentLimit.toString());
                params.append('sort', currentSort);

                // Add search and filter parameters if they exist
                const searchInput = document.getElementById('search-input');
                const statusFilter = document.getElementById('status-filter');
                const favoriteFilter = document.getElementById('favorite-filter');

                if (searchInput && searchInput.value.trim()) {
                    params.append('q', searchInput.value.trim());
                }
                if (statusFilter && statusFilter.value) {
                    params.append('status', statusFilter.value);
                }
                if (favoriteFilter && favoriteFilter.value) {
                    params.append('favorite', favoriteFilter.value);
                }

                const api = new ApiClient();
                const response = await api.get(`/api/saves?${params.toString()}`);

                loading.style.display = 'none';

                if (response.items && response.items.length > 0) {
                    displaySaves(response.items);
                    resultsCount.textContent = `${response.total} items found`;
                } else {
                    container.innerHTML = `
                        <div class="text-center py-5">
                            <h4>No saves yet</h4>
                            <p class="text-muted">Start saving URLs to see them here!</p>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUrlModal">
                                Add Your First URL
                            </button>
                        </div>
                    `;
                    resultsCount.textContent = '0 items';
                }
            } catch (error) {
                loading.style.display = 'none';
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <h5>Error Loading Saves</h5>
                        <p>${error.message}</p>
                        <button class="btn btn-outline-danger" onclick="loadSaves()">Try Again</button>
                    </div>
                `;
                resultsCount.textContent = 'Error';
            }
        }

        function displaySaves(items) {
            const container = document.getElementById('saves-container');
            container.innerHTML = items.map(item => {
                const thumbnailHtml = item.image_url ?
                    `<img src="${item.image_url}" alt="Thumbnail" class="saved-item-thumbnail" onerror="this.style.display='none'">` : '';

                return `
                    <div class="saved-item ${currentView}" data-id="${item.id}">
                        ${currentView === 'list' && thumbnailHtml ? thumbnailHtml : ''}
                        <div class="saved-item-content">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    ${currentView === 'compact' && thumbnailHtml ? `<div class="d-flex gap-2 align-items-start">${thumbnailHtml}<div class="flex-grow-1">` : '<div>'}
                                        <h5 class="saved-item-title">
                                            <a href="${item.url}" target="_blank" class="text-decoration-none">
                                                ${item.title || item.url}
                                            </a>
                                        </h5>
                                        <a href="${item.url}" class="saved-item-url" target="_blank">${item.url}</a>
                                        ${item.excerpt && currentView !== 'compact' ? `<p class="saved-item-excerpt">${item.excerpt}</p>` : ''}
                                        <div class="saved-item-meta">
                                            <span class="status-badge badge bg-secondary">${item.status}</span>
                                            <span class="text-muted ms-2" title="Saved: ${new Date(item.created_at).toLocaleString()}">
                                                ${new Date(item.created_at).toLocaleDateString()}
                                            </span>
                                        </div>
                                    ${currentView === 'compact' && thumbnailHtml ? '</div></div>' : '</div>'}
                                </div>
                                <div class="ms-3 d-flex gap-1">
                                    <button class="btn-favorite ${item.is_favorite ? 'active' : ''}"
                                            onclick="toggleFavorite(${item.id})" title="Toggle favorite">
                                        ⭐
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger"
                                            onclick="deleteSavedItem(${item.id})" title="Delete">
                                        🗑️
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

            // Apply current view
            updateItemsView(currentView);
        }

        function updateItemsView(view) {
            currentView = view;
            const items = document.querySelectorAll('.saved-item');
            items.forEach(item => {
                item.className = `saved-item ${view}`;
            });

            // Update view toggle buttons
            document.querySelectorAll('.view-toggle .btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.view === view);
            });
        }

        async function saveNewUrl() {
            const urlInput = document.getElementById('url-input');
            const saveBtn = document.getElementById('save-url-btn');
            const spinner = document.getElementById('save-url-spinner');
            const errorDiv = document.getElementById('add-url-error');

            const url = urlInput.value.trim();
            if (!url) return;

            try {
                saveBtn.disabled = true;
                spinner.classList.remove('d-none');
                errorDiv.classList.add('d-none');

                const api = new ApiClient();
                await api.post('/api/save', { url: url });

                // Close modal and reload saves
                const modal = bootstrap.Modal.getInstance(document.getElementById('addUrlModal'));
                modal.hide();
                urlInput.value = '';

                showToast('URL saved successfully!', 'success');
                loadSaves();

            } catch (error) {
                errorDiv.textContent = error.message;
                errorDiv.classList.remove('d-none');
            } finally {
                saveBtn.disabled = false;
                spinner.classList.add('d-none');
            }
        }

        // Override the search manager's performSearch to use our loadSaves
        window.addEventListener('load', function() {
            if (window.searchManager) {
                window.searchManager.performSearch = loadSaves;
            }
        });
    </script>
</body>
</html>
