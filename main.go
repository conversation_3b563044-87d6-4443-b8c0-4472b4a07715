package main

import (
	"log"
	"os"

	"save-it/handlers"
	"save-it/middleware"
	"save-it/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Database instance
var db *gorm.DB

func main() {
	// Initialize database
	var err error
	db, err = models.InitDatabase("save-it.db")
	if err != nil {
		log.Fatal("Failed to initialize database:", err)
	}

	// Initialize Gin router
	r := gin.Default()

	// Set max multipart memory to 50MB for file uploads
	r.MaxMultipartMemory = 50 << 20 // 50 MB

	// Load HTML templates
	r.LoadHTMLGlob("templates/*")

	// Serve static files
	r.Static("/static", "./static")

	// Basic routes
	r.GET("/", func(c *gin.Context) {
		c.HTML(200, "index.html", gin.H{
			"title": "Save-It - Your Personal URL Saver",
		})
	})

	// Authentication pages
	r.GET("/login", func(c *gin.Context) {
		c.HTML(200, "login.html", nil)
	})

	r.GET("/register", func(c *gin.Context) {
		c.HTML(200, "register.html", nil)
	})

	r.GET("/saves", func(c *gin.Context) {
		c.HTML(200, "saves.html", nil)
	})

	r.GET("/import", func(c *gin.Context) {
		c.HTML(200, "import.html", gin.H{
			"title": "Import Data - Save-It",
		})
	})

	// Health check endpoint
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"message": "Save-It server is running",
		})
	})

	// Initialize handlers
	authHandler := handlers.NewAuthHandler(db)
	savesHandler := handlers.NewSavesHandler(db)
	importHandler := handlers.NewImportHandler(db)

	// API routes
	api := r.Group("/api")
	{
		// Authentication routes (no auth required)
		api.POST("/register", authHandler.Register)
		api.POST("/login", authHandler.Login)
		api.POST("/validate-token", authHandler.ValidateToken)

		// Protected routes (require authentication)
		protected := api.Group("/")
		protected.Use(middleware.AuthMiddleware(db))
		{
			// User routes
			protected.GET("/user", authHandler.GetCurrentUser)
			protected.POST("/refresh-token", authHandler.RefreshToken)
			protected.POST("/logout", authHandler.Logout)

			// Saved items routes
			protected.POST("/save", savesHandler.SaveURL)
			protected.GET("/saves", savesHandler.GetSavedItems)
			protected.GET("/save/:id", savesHandler.GetSavedItem)
			protected.PUT("/save/:id", savesHandler.UpdateSavedItem)
			protected.DELETE("/save/:id", savesHandler.DeleteSavedItem)

			// Import routes
			protected.POST("/import/pocket", importHandler.ImportPocketData)
		}
	}

	// Get port from environment or default to 9191
	port := os.Getenv("PORT")
	if port == "" {
		port = "9191"
	}

	log.Printf("Starting Save-It server on port %s", port)
	log.Printf("Access the application at: http://localhost:%s", port)

	// Start server
	if err := r.Run(":" + port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
